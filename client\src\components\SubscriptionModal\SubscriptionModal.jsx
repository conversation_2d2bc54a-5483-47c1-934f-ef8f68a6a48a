import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { message } from 'antd';
import { getPlans } from '../../apicalls/plans';
import { addPayment, checkPaymentStatus } from '../../apicalls/payment';
import { updateUserInfo } from '../../apicalls/users';
import axiosInstance from '../../apicalls/index';
import { SetSubscription } from '../../redux/subscriptionSlice';
import { SetUser } from '../../redux/usersSlice';
import { HideLoading, ShowLoading } from '../../redux/loaderSlice';
import './SubscriptionModal.css';

const SubscriptionModal = ({ isOpen, onClose, onSuccess }) => {
  const [plans, setPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [step, setStep] = useState('plans'); // 'plans', 'payment', 'success'
  const [paymentPhone, setPaymentPhone] = useState('');
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [phoneUpdated, setPhoneUpdated] = useState(false);

  // Validate phone number format
  const isValidPhone = (phone) => {
    return phone && /^(06|07)\d{8}$/.test(phone);
  };

  // Update user's phone number in profile (simplified approach)
  const updateUserPhoneNumber = async (newPhone) => {
    try {
      console.log('📱 Updating user phone number in profile:', newPhone);
      console.log('👤 Current user data:', user);

      // Ensure we have all required fields
      if (!user._id) {
        console.error('❌ User ID is missing');
        return false;
      }

      // Try direct API call first
      console.log('🔄 Attempting direct API call...');

      const directPayload = {
        userId: user._id,
        name: user.name || 'Unknown',
        email: user.email || '',
        school: user.school || '',
        class_: user.class || user.className || '',
        level: user.level || 'Primary',
        phoneNumber: newPhone
      };

      console.log('📤 Direct API payload:', directPayload);

      const directResponse = await axiosInstance.post('/api/users/update-user-info', directPayload);

      console.log('📥 Direct API response:', directResponse.data);

      if (directResponse.data.success) {
        // Update Redux store with new user data
        dispatch(SetUser(directResponse.data.data));

        // Update localStorage
        localStorage.setItem('user', JSON.stringify(directResponse.data.data));

        console.log('✅ User phone number updated successfully (direct API)');
        console.log('📱 New user data:', directResponse.data.data);
        return true;
      } else {
        console.error('❌ Direct API failed, trying updateUserInfo...');

        // Fallback to original method
        const response = await updateUserInfo(directPayload);

        console.log('📥 Fallback response:', response);

        if (response.success) {
          // Update Redux store with new user data
          dispatch(SetUser(response.data));

          // Update localStorage
          localStorage.setItem('user', JSON.stringify(response.data));

          console.log('✅ User phone number updated successfully (fallback)');
          console.log('📱 New user data:', response.data);
          return true;
        } else {
          console.error('❌ Both methods failed');
          console.error('❌ Direct response:', directResponse.data);
          console.error('❌ Fallback response:', response);
          return false;
        }
      }
    } catch (error) {
      console.error('❌ Error updating user phone number:', error);
      console.error('❌ Error details:', error.response?.data);

      // Show specific error message
      if (error.response?.data?.message) {
        message.error(`Update failed: ${error.response.data.message}`);
      }

      return false;
    }
  };
  
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  useEffect(() => {
    if (isOpen) {
      fetchPlans();
      // Initialize payment phone with user's current phone
      setPaymentPhone(user?.phoneNumber || '');
    }
  }, [isOpen, user?.phoneNumber]);

  // Update payment phone when user data changes (after profile update)
  useEffect(() => {
    if (user?.phoneNumber && !isEditingPhone) {
      setPaymentPhone(user.phoneNumber);
    }
  }, [user?.phoneNumber, isEditingPhone]);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await getPlans();
      setPlans(Array.isArray(response) ? response : []);
    } catch (error) {
      console.error('Error fetching plans:', error);
      message.error('Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
    setStep('payment');
  };

  const handlePayment = async () => {
    if (!selectedPlan) {
      message.error('Please select a plan first');
      return;
    }

    if (!paymentPhone || paymentPhone.length < 10) {
      message.error('Please enter a valid phone number (e.g., 0744963858)');
      return;
    }

    // Validate Tanzanian phone number format
    if (!/^(06|07)\d{8}$/.test(paymentPhone)) {
      message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');
      return;
    }

    try {
      setPaymentLoading(true);
      dispatch(ShowLoading());

      const paymentData = {
        plan: selectedPlan,
        userId: user._id,
        userPhone: paymentPhone, // Use the payment phone number (may be different from profile)
        userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
      };

      const response = await addPayment(paymentData);

      if (response.success) {
        message.success('Payment initiated! Please check your phone for SMS confirmation.');
        setStep('success');
        
        // Start checking payment status
        checkPaymentConfirmation(response.order_id);
      } else {
        throw new Error(response.message || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      message.error(error.message || 'Payment failed. Please try again.');
    } finally {
      setPaymentLoading(false);
      dispatch(HideLoading());
    }
  };

  const checkPaymentConfirmation = async (orderId) => {
    let attempts = 0;
    const maxAttempts = 120; // 10 minutes (increased for better user experience)

    const checkStatus = async () => {
      try {
        attempts++;
        console.log(`🔍 Checking payment status... Attempt ${attempts}/${maxAttempts}`);

        const response = await checkPaymentStatus();
        console.log('📥 Payment status response:', response);

        if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {
          console.log('✅ Payment confirmed! Showing success...');

          // Update Redux store
          dispatch(SetSubscription(response));

          // Show success message with celebration
          message.success({
            content: '🎉 Payment Confirmed! Welcome to Premium!',
            duration: 5,
            style: {
              marginTop: '20vh',
              fontSize: '16px',
              fontWeight: '600'
            }
          });

          // Trigger success callback
          onSuccess && onSuccess();

          // Close modal after short delay to show success
          setTimeout(() => {
            onClose();
          }, 2000);

          return true;
        }

        if (attempts >= maxAttempts) {
          console.log('⏰ Payment check timeout reached');
          message.warning({
            content: 'Payment is still processing. Your subscription will activate automatically when payment is complete.',
            duration: 8
          });
          return false;
        }

        // Continue checking
        setTimeout(checkStatus, 3000); // Check every 3 seconds for faster response
      } catch (error) {
        console.error('❌ Error checking payment status:', error);
        if (attempts >= maxAttempts) {
          message.error('Unable to verify payment. Please contact support if payment was completed.');
        } else {
          setTimeout(checkStatus, 3000);
        }
      }
    };

    // Start checking immediately
    checkStatus();
  };

  const handleClose = () => {
    setStep('plans');
    setSelectedPlan(null);
    setPaymentLoading(false);
    setIsEditingPhone(false);
    setPhoneUpdated(false);
    setPaymentPhone(user?.phoneNumber || '');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="subscription-modal-overlay">
      <div className="subscription-modal">
        <div className="modal-header">
          <h2 className="modal-title">
            {step === 'plans' && '🚀 Choose Your Learning Plan'}
            {step === 'payment' && '💳 Complete Your Payment'}
            {step === 'success' && '⏳ Processing Payment...'}
          </h2>
          <button className="close-button" onClick={handleClose}>×</button>
        </div>

        <div className="modal-content">
          {step === 'plans' && (
            <div className="plans-grid">
              {loading ? (
                <div className="loading-state">
                  <div className="spinner"></div>
                  <p>Loading plans...</p>
                </div>
              ) : (
                plans.map((plan) => (
                  <div key={plan._id} className="plan-card" onClick={() => handlePlanSelect(plan)}>
                    <div className="plan-header">
                      <h3 className="plan-title">{plan.title}</h3>
                      {plan.title?.toLowerCase().includes('glimp') && (
                        <span className="plan-badge">🔥 Popular</span>
                      )}
                    </div>
                    
                    <div className="plan-price">
                      <span className="price-amount">{plan.discountedPrice?.toLocaleString()} TZS</span>
                      {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (
                        <span className="price-original">{plan.actualPrice.toLocaleString()} TZS</span>
                      )}
                      <span className="price-period">{plan.duration} month{plan.duration > 1 ? 's' : ''}</span>
                    </div>

                    <div className="plan-features">
                      {plan.features?.slice(0, 4).map((feature, index) => (
                        <div key={index} className="feature">
                          <span className="feature-icon">✓</span>
                          <span className="feature-text">{feature}</span>
                        </div>
                      ))}
                      {plan.features?.length > 4 && (
                        <div className="feature">
                          <span className="feature-icon">+</span>
                          <span className="feature-text">{plan.features.length - 4} more features</span>
                        </div>
                      )}
                    </div>

                    <button className="select-plan-btn">
                      Choose {plan.title}
                    </button>
                  </div>
                ))
              )}
            </div>
          )}

          {step === 'payment' && selectedPlan && (
            <div className="payment-step">
              <div className="selected-plan-summary">
                <h3>Selected Plan: {selectedPlan.title}</h3>
                <p className="plan-price-summary">
                  {selectedPlan.discountedPrice?.toLocaleString()} TZS for {selectedPlan.duration} month{selectedPlan.duration > 1 ? 's' : ''}
                </p>
              </div>

              <div className="payment-info">
                <div className="phone-section">
                  <div className="info-item">
                    <span className="info-label">Phone Number for Payment:</span>
                    {!isEditingPhone ? (
                      <div className="phone-display">
                        <span className={`info-value ${phoneUpdated ? 'updated' : ''}`}>
                          {paymentPhone || 'Not provided'}
                          {phoneUpdated && <span className="updated-indicator">✅ Updated</span>}
                        </span>
                        <button
                          className="edit-phone-btn"
                          onClick={() => setIsEditingPhone(true)}
                          type="button"
                        >
                          ✏️ Change
                        </button>
                      </div>
                    ) : (
                      <div className="phone-edit">
                        <input
                          type="tel"
                          value={paymentPhone}
                          onChange={(e) => setPaymentPhone(e.target.value)}
                          placeholder="Enter phone number (e.g., 0744963858)"
                          className={`phone-input ${paymentPhone ? (isValidPhone(paymentPhone) ? 'valid' : 'invalid') : ''}`}
                          maxLength="10"
                        />
                        {paymentPhone && (
                          <div className={`phone-validation ${isValidPhone(paymentPhone) ? 'valid' : 'invalid'}`}>
                            {isValidPhone(paymentPhone) ? (
                              <span className="validation-message valid">✅ Valid phone number</span>
                            ) : (
                              <span className="validation-message invalid">❌ Must start with 06 or 07 and be 10 digits</span>
                            )}
                          </div>
                        )}
                        <div className="phone-actions">
                          <button
                            className="save-phone-btn"
                            onClick={async () => {
                              if (isValidPhone(paymentPhone)) {
                                try {
                                  // Show loading state
                                  const btn = document.querySelector('.save-phone-btn');
                                  const originalText = btn.textContent;
                                  btn.textContent = '⏳ Saving...';
                                  btn.disabled = true;

                                  console.log('💾 Starting phone number save process...');
                                  console.log('📱 Payment phone:', paymentPhone);
                                  console.log('👤 User phone:', user?.phoneNumber);

                                  // Check if phone number is different from current user's phone
                                  const isPhoneChanged = paymentPhone !== user?.phoneNumber;
                                  console.log('🔄 Phone changed:', isPhoneChanged);

                                  if (isPhoneChanged) {
                                    console.log('📞 Updating user profile with new phone number...');
                                    // Update user's profile with new phone number
                                    const updateSuccess = await updateUserPhoneNumber(paymentPhone);

                                    if (updateSuccess) {
                                      console.log('✅ Phone number update successful');
                                      setIsEditingPhone(false);
                                      setPhoneUpdated(true);

                                      // Show success messages
                                      message.success({
                                        content: '📱 Phone number updated successfully!',
                                        duration: 4,
                                        style: {
                                          marginTop: '20vh',
                                          fontSize: '15px',
                                          fontWeight: '600'
                                        }
                                      });

                                      // Additional success message for payment
                                      setTimeout(() => {
                                        message.info({
                                          content: '💡 Your profile has been updated. This number will receive payment SMS.',
                                          duration: 5,
                                          style: {
                                            marginTop: '20vh',
                                            fontSize: '14px'
                                          }
                                        });
                                      }, 1000);

                                      // Reset the updated indicator after 5 seconds
                                      setTimeout(() => {
                                        setPhoneUpdated(false);
                                      }, 5000);
                                    } else {
                                      console.log('❌ Phone number update failed');
                                      message.error('Failed to update phone number in profile. Please try again.');

                                      // Restore button state on failure
                                      btn.textContent = originalText;
                                      btn.disabled = !isValidPhone(paymentPhone);
                                    }
                                  } else {
                                    console.log('📱 Phone number is the same, just confirming...');
                                    // Phone number is the same, just close editing
                                    setIsEditingPhone(false);
                                    message.success({
                                      content: '📱 Phone number confirmed for payment',
                                      duration: 3,
                                      style: {
                                        marginTop: '20vh',
                                        fontSize: '15px',
                                        fontWeight: '600'
                                      }
                                    });

                                    // Restore button state
                                    btn.textContent = originalText;
                                    btn.disabled = !isValidPhone(paymentPhone);
                                  }
                                } catch (error) {
                                  console.error('❌ Error saving phone number:', error);
                                  message.error('Failed to save phone number. Please try again.');

                                  // Restore button state on error
                                  const btn = document.querySelector('.save-phone-btn');
                                  if (btn) {
                                    btn.textContent = '✅ Save';
                                    btn.disabled = !isValidPhone(paymentPhone);
                                  }
                                }
                              } else {
                                message.error('Please enter a valid Tanzanian phone number (06xxxxxxxx or 07xxxxxxxx)');
                              }
                            }}
                            disabled={!isValidPhone(paymentPhone)}
                            type="button"
                          >
                            ✅ Save
                          </button>
                          <button
                            className="cancel-phone-btn"
                            onClick={() => {
                              setPaymentPhone(user?.phoneNumber || '');
                              setIsEditingPhone(false);
                            }}
                            type="button"
                          >
                            ❌ Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="phone-note">
                    <small>💡 This number will receive the payment SMS. You can use a different number than your profile.</small>
                  </div>
                </div>

                <div className="info-item">
                  <span className="info-label">Payment Method:</span>
                  <span className="info-value">Mobile Money (M-Pesa, Tigo Pesa, Airtel Money)</span>
                </div>
              </div>

              <div className="payment-actions">
                <button className="back-btn" onClick={() => setStep('plans')}>
                  ← Back to Plans
                </button>
                <button
                  className="pay-btn"
                  onClick={handlePayment}
                  disabled={paymentLoading || !paymentPhone || isEditingPhone}
                >
                  {paymentLoading ? (
                    <>
                      <span className="btn-spinner"></span>
                      Processing...
                    </>
                  ) : isEditingPhone ? (
                    'Save phone number first'
                  ) : !paymentPhone ? (
                    'Enter phone number'
                  ) : (
                    `Pay ${selectedPlan.discountedPrice?.toLocaleString()} TZS`
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'success' && (
            <div className="success-step">
              <div className="success-animation">
                <div className="pulse-circle">
                  <div className="phone-icon">📱</div>
                </div>
              </div>
              
              <h3>Payment Request Sent!</h3>
              <p>Please check your phone for SMS confirmation and complete the payment.</p>
              
              <div className="payment-steps">
                <div className="step">
                  <span className="step-number">1</span>
                  <span className="step-text">Check your phone for SMS</span>
                </div>
                <div className="step">
                  <span className="step-number">2</span>
                  <span className="step-text">Follow the payment instructions</span>
                </div>
                <div className="step">
                  <span className="step-number">3</span>
                  <span className="step-text">Your subscription will activate automatically</span>
                </div>
              </div>

              <div className="success-actions">
                <button
                  className="check-status-btn"
                  onClick={async () => {
                    console.log('🔍 Manual payment check triggered');
                    try {
                      const response = await checkPaymentStatus();
                      console.log('📥 Manual check response:', response);

                      if (response && !response.error && response.paymentStatus === 'paid' && response.status === 'active') {
                        console.log('✅ Payment confirmed manually!');
                        dispatch(SetSubscription(response));
                        message.success('🎉 Payment Confirmed! Welcome to Premium!');
                        onSuccess && onSuccess();
                        setTimeout(() => onClose(), 1000);
                      } else {
                        message.info('Payment not yet confirmed. Please complete the mobile money transaction.');
                      }
                    } catch (error) {
                      console.error('❌ Manual check error:', error);
                      message.error('Error checking payment status');
                    }
                  }}
                >
                  ✅ Check Payment Status
                </button>

                <button className="done-btn" onClick={handleClose}>
                  I'll complete the payment
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionModal;

/* Overall Container */
.plans-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    padding-top: 40px;
    padding-bottom: 40px;
}

/* Mobile First Responsive Design */
@media (max-width: 480px) {
    .plans-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 12px;
        padding-top: 20px;
        padding-bottom: 20px;
    }
}

/* Tablet */
@media (min-width: 481px) and (max-width: 768px) {
    .plans-container {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        padding: 16px;
        max-width: 100%;
    }
}

/* Small Laptop */
@media (min-width: 769px) and (max-width: 1024px) {
    .plans-container {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        padding: 20px;
        max-width: 1000px;
    }
}

/* Large Laptop and Desktop */
@media (min-width: 1025px) {
    .plans-container {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 28px;
        padding: 24px;
        max-width: 1280px;
    }
}

/* Plan Card */
.plan-card {
    position: relative; /* For the "Most Popular" label */
    border: 2px solid #e5e7eb;
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(145deg, #ffffff, #f8f9fb);
    transition: all 0.3s ease;
    overflow: hidden;
    width: 100%;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.plan-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #007BFF;
}

/* Mobile Plan Card */
@media (max-width: 480px) {
    .plan-card {
        padding: 20px;
        border-radius: 16px;
        min-height: 450px;
    }

    .plan-card:hover {
        transform: translateY(-4px);
    }
}

/* Tablet Plan Card */
@media (min-width: 481px) and (max-width: 768px) {
    .plan-card {
        padding: 22px;
        min-height: 480px;
    }
}

/* Highlight Popular Plan */
.plan-card.basic {
    background: linear-gradient(145deg, #f3e8ff, #e9d5ff);
    border: 2px solid #a78bfa;
    transform: scale(1.02);
}

/* Glimp Plan Styling */
.plan-card.glimp {
    background: linear-gradient(145deg, #e6f7ff, #bae7ff);
    border: 2px solid #1890ff;
    position: relative;
}

.plan-card.glimp::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #52c41a);
}

/* Most Popular Label */
.most-popular-label {
    position: absolute;
    top: -15px; /* Adjusted to appear above the card */
    left: 50%;
    transform: translateX(-50%);
    background-color: #213864;
    color: #fff;
    font-size: 1.3rem;
    font-weight: bold;
    padding: 5px 14px;
    border-radius: 12px;
    text-transform: uppercase;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    width: 200px;
    text-align: center;
}

/* Title */
.plan-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #0F3460;
    margin-bottom: 8px;
    text-align: center;
}

/* Prices */
.plan-actual-price {
    font-size: 1.125rem;
    font-weight: 600;
    color: #9ca3af;
    text-decoration: line-through;
    margin: 6px 0;
    text-align: center;
    display: block;
}

.plan-discounted-price {
    font-size: 2.25rem;
    font-weight: bold;
    color: #283965;
    margin: 12px 0;
    text-align: center;
}

.plan-discount-tag {
    background-color: #ede9fe;
    color: #6b46c1;
    font-size: 0.875rem;
    font-weight: bold;
    padding: 6px 12px;
    border-radius: 12px;
    display: inline-block;
    margin: 0 auto;
    text-align: center;
}

.plan-renewal-info {
    margin-top: 8px;
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

/* Button */
.plan-button {
    background: linear-gradient(145deg, #0F3460, #283965);
    color: white;
    font-size: 1rem;
    font-weight: bold;
    padding: 12px 16px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    margin: 16px auto 0;
    display: block;
    width: 100%;
    text-align: center;
    transition: background-color 0.3s ease;
}

.plan-button:hover {
    background: linear-gradient(145deg, #283965, #3a0e78);
}

/* Features */
.plan-features {
    margin-top: 16px;
    font-size: 0.9rem;
    color: #4b5563;
    list-style: none;
    padding: 0;
}

.plan-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.plan-feature-icon {
    color: #10b981;
    font-size: 1.1rem;
}

/* Small Screens (Responsive Breakpoints) */
@media (max-width: 768px) {
    .plan-card {
        padding: 16px;
        border-radius: 12px;
    }

    .plan-title {
        font-size: 1.1rem;
    }

    .plan-discounted-price {
        font-size: 2rem;
    }

    .plan-button {
        font-size: 0.9rem;
        padding: 10px 14px;
    }

    .plan-features {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .plans-container {
        padding: 12px;
        gap: 16px;
    }

    .plan-card {
        padding: 12px;
    }

    .plan-title {
        font-size: 1rem;
    }

    .plan-discounted-price {
        font-size: 1.75rem;
    }

    .plan-features {
        font-size: 0.8rem;
    }

    .plan-button {
        font-size: 0.85rem;
        padding: 8px 12px;
    }
}


  .subscription-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: #f9fafb; /* Light gray background */
    border: 1px solid #e5e7eb; /* Border with subtle contrast */
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Soft shadow for depth */
    max-width: 400px;
    margin: 20px auto;
    text-align: center;
  }
  
  .plan-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: #253864; /* Navy Blue */
    margin-bottom: 15px;
  }
  
  .plans-container svg {
    margin: 20px 0;
  }
  
  .plan-description {
    font-size: 1rem;
    color: #4b5563; /* Neutral dark gray */
    margin-bottom: 10px;
  }
  
  .plan-dates {
    font-size: 0.9rem;
    color: #6b7280; /* Lighter gray for secondary information */
    margin: 5px 0;
  }
  

  .no-plan-required {
    background-color: #f9f9f9; /* Light background to differentiate it from other content */
    border-radius: 8px; /* Rounded corners for a soft, modern look */
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Light shadow for depth */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  
  .no-plan-content {
    max-width: 600px; /* Maximum width for better readability */
  }
  .no-plan-required h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: bold;
  }
  
  .no-plan-required p {
    font-size: 1rem;
    color: #34495e;
    line-height: 1.6;
  }
  
  /* Additional responsive styling */
  @media (max-width: 768px) {
    .no-plan-required {
      padding: 15px;
    }
  
    .no-plan-required h2 {
      font-size: 1.6rem;
    }
  
    .no-plan-required p {
      font-size: 0.95rem;
    }
  }

/* ===== NEW ENHANCED PLAN STYLES ===== */

/* Glimp Plan Label */
.glimp-label {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #1890ff, #52c41a);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: bold;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(24, 144, 255, 0.4);
    animation: glow 2s ease-in-out infinite alternate;
    z-index: 10;
}

@keyframes glow {
    from { box-shadow: 0 4px 15px rgba(24, 144, 255, 0.4); }
    to { box-shadow: 0 4px 20px rgba(24, 144, 255, 0.8); }
}

/* Plan Header */
.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

/* Duration Highlight */
.plan-duration-highlight {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, #007BFF, #0056D2);
    color: white;
    padding: 12px 16px;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    min-width: 80px;
    animation: bounce 2s ease-in-out infinite;
    flex-shrink: 0;
}

/* Mobile Plan Header */
@media (max-width: 480px) {
    .plan-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .plan-duration-highlight {
        padding: 10px 14px;
        min-width: 70px;
    }

    .duration-number {
        font-size: 1.8rem !important;
    }

    .duration-text {
        font-size: 0.7rem !important;
    }
}

/* Tablet Plan Header */
@media (min-width: 481px) and (max-width: 768px) {
    .plan-header {
        gap: 12px;
    }

    .plan-duration-highlight {
        min-width: 75px;
    }
}

.duration-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.duration-text {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* Plan Pricing */
.plan-pricing {
    text-align: center;
    margin-bottom: 15px;
}

/* Plan Value */
.plan-value {
    text-align: center;
    margin-bottom: 20px;
}

.value-text {
    background: linear-gradient(135deg, #52c41a, #389e0d);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
}

/* Enhanced Plan Button */
.plan-card.glimp .plan-button {
    background: linear-gradient(135deg, #1890ff, #52c41a);
    border: none;
    color: white;
    font-weight: bold;
    transform: scale(1.05);
    animation: pulse-button 2s infinite;
}

@keyframes pulse-button {
    0% { transform: scale(1.05); }
    50% { transform: scale(1.08); }
    100% { transform: scale(1.05); }
}

.plan-card.glimp .plan-button:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.4);
}

/* ===== RESPONSIVE TYPOGRAPHY AND ELEMENTS ===== */

/* Mobile Typography */
@media (max-width: 480px) {
    .plan-title {
        font-size: 1.4rem !important;
        margin-bottom: 10px;
    }

    .plan-actual-price {
        font-size: 0.9rem !important;
    }

    .plan-discounted-price {
        font-size: 1.6rem !important;
    }

    .plan-discount-tag {
        font-size: 0.8rem !important;
        padding: 4px 8px !important;
    }

    .value-text {
        font-size: 0.8rem !important;
        padding: 5px 10px !important;
    }

    .plan-button {
        font-size: 0.9rem !important;
        padding: 12px 20px !important;
        width: 100%;
        margin-top: auto;
    }

    .plan-features {
        font-size: 0.85rem !important;
    }

    .plan-feature {
        padding: 6px 0 !important;
    }

    .most-popular-label,
    .glimp-label {
        font-size: 10px !important;
        padding: 6px 16px !important;
        top: -12px !important;
    }
}

/* Tablet Typography */
@media (min-width: 481px) and (max-width: 768px) {
    .plan-title {
        font-size: 1.5rem !important;
    }

    .plan-discounted-price {
        font-size: 1.8rem !important;
    }

    .plan-button {
        font-size: 0.95rem !important;
        padding: 12px 24px !important;
    }
}

/* Small Laptop */
@media (min-width: 769px) and (max-width: 1024px) {
    .plan-title {
        font-size: 1.6rem !important;
    }

    .plan-discounted-price {
        font-size: 1.9rem !important;
    }
}

/* ===== RESPONSIVE LAYOUT IMPROVEMENTS ===== */

/* Mobile Layout */
@media (max-width: 480px) {
    .plan-pricing {
        margin-bottom: 12px;
    }

    .plan-value {
        margin-bottom: 15px;
    }

    .plan-features {
        flex-grow: 1;
        margin-bottom: 15px;
    }
}

/* Ensure buttons are at bottom on all devices */
.plan-card {
    display: flex;
    flex-direction: column;
}

.plan-features {
    flex-grow: 1;
    margin-bottom: 20px;
}

.plan-button {
    margin-top: auto;
}

/* Current Subscription Container */
.current-subscription-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 32px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

/* Current Plan Header */
.current-plan-header {
    text-align: center;
    margin-bottom: 32px;
}

.plan-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #16a34a;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 16px;
    border: 1px solid #86efac;
}

.status-icon {
    display: flex;
    align-items: center;
}

.current-plan-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 16px 0 8px 0;
    background: linear-gradient(135deg, #007BFF, #0056D2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.current-plan-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin: 0;
}

/* Plan Details Card */
.current-plan-details {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 20px;
    padding: 32px;
    border: 1px solid #e2e8f0;
}

.plan-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.plan-info-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: white;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.plan-info-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 123, 255, 0.1);
}

.info-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.info-value.status-active {
    color: #16a34a;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.9rem;
}

/* Current Plan Features */
.current-plan-features {
    margin-bottom: 32px;
}

.features-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 20px;
    text-align: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.feature-benefit {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.feature-benefit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.1);
}

.benefit-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.benefit-text {
    font-size: 0.95rem;
    font-weight: 500;
    color: #475569;
}

/* Current Plan Actions */
.current-plan-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.action-btn {
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 180px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.action-btn.secondary {
    background: white;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced Responsive Design for Current Plan */
@media (max-width: 480px) {
    .current-subscription-container {
        padding: 20px 16px;
        margin: 16px;
        border-radius: 20px;
    }

    .current-plan-title {
        font-size: 1.8rem;
    }

    .current-plan-subtitle {
        font-size: 1rem;
    }

    .current-plan-details {
        padding: 20px 16px;
    }

    .plan-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 24px;
    }

    .plan-info-item {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .info-icon {
        font-size: 1.3rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .feature-benefit {
        padding: 12px;
        justify-content: center;
    }

    .current-plan-actions {
        flex-direction: column;
        gap: 12px;
    }

    .action-btn {
        min-width: auto;
        width: 100%;
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .plan-status-badge {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .current-subscription-container {
        padding: 28px 24px;
        margin: 20px;
    }

    .current-plan-title {
        font-size: 2.2rem;
    }

    .current-plan-details {
        padding: 28px 24px;
    }

    .plan-info-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .current-plan-actions {
        flex-direction: column;
        gap: 14px;
    }

    .action-btn {
        min-width: auto;
        width: 100%;
    }
}

@media (min-width: 769px) {
    .plan-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Enhanced Mobile Responsive for Plans Grid */
@media (max-width: 768px) {
    .plans-container {
        padding: 10px;
    }

    .plans-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .plan-card {
        padding: 20px 15px;
    }

    .plan-title {
        font-size: 1.3rem;
    }

    .plan-price {
        font-size: 1.8rem;
    }

    .plan-features {
        font-size: 0.85rem;
    }

    .plan-button {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}
/* Modal Overlay */
.confirm-modal-overlay {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 16px;
}

/* Modal Content */
.confirm-modal-content {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 24px;
    padding: 32px;
    max-width: 550px;
    width: 100%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    animation: successModalSlideIn 0.5s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

/* Success Header */
.success-header {
    margin-bottom: 32px;
}

.success-icon-container {
    margin-bottom: 24px;
}

.success-checkmark {
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #4ade80;
    stroke-miterlimit: 10;
    animation: checkmark-fill 0.4s ease-in-out 0.4s forwards, checkmark-scale 0.3s ease-in-out 0.9s both;
}

.checkmark-circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    stroke-width: 2;
    stroke-miterlimit: 10;
    stroke: #4ade80;
    fill: none;
    animation: checkmark-stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark-check {
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    animation: checkmark-stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}

.modal-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin: 16px 0 8px 0;
    line-height: 1.2;
}

.modal-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

/* Success Features */
.success-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin: 32px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 16px;
    border: 1px solid #bae6fd;
    transition: all 0.3s ease;
    text-align: left;
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 123, 255, 0.15);
}

.feature-icon {
    font-size: 2rem;
    margin-right: 16px;
    flex-shrink: 0;
}

.feature-text h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.feature-text p {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
    line-height: 1.4;
}

/* Payment Details */
.modal-details {
    margin: 32px 0;
    text-align: left;
}

.details-header {
    margin-bottom: 20px;
    text-align: center;
}

.details-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.details-grid {
    display: grid;
    gap: 16px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.detail-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.success-status {
    color: #16a34a;
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Action Buttons */
.modal-actions {
    display: flex;
    gap: 12px;
    margin-top: 32px;
}

.action-button {
    flex: 1;
    padding: 14px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-button.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.action-button.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.action-button.secondary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #475569;
    border: 1px solid #e2e8f0;
}

.action-button.secondary:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-2px);
}

/* Animations */
@keyframes successModalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes checkmark-stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes checkmark-scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes checkmark-fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #4ade80;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .confirm-modal-content {
        margin: 16px;
        padding: 24px 20px;
        max-width: calc(100vw - 32px);
        border-radius: 20px;
    }

    .success-checkmark {
        width: 64px;
        height: 64px;
    }

    .checkmark {
        width: 64px;
        height: 64px;
    }

    .modal-title {
        font-size: 1.6rem;
    }

    .modal-subtitle {
        font-size: 1rem;
    }

    .success-features {
        grid-template-columns: 1fr;
        gap: 12px;
        margin: 24px 0;
    }

    .feature-item {
        padding: 16px;
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        margin-right: 0;
        margin-bottom: 8px;
        font-size: 1.8rem;
    }

    .feature-text h4 {
        font-size: 0.9rem;
    }

    .feature-text p {
        font-size: 0.8rem;
    }

    .detail-item {
        flex-direction: column;
        gap: 8px;
        text-align: center;
        padding: 12px 16px;
    }

    .detail-label {
        font-size: 0.7rem;
    }

    .detail-value {
        font-size: 0.9rem;
    }

    .modal-actions {
        flex-direction: column;
        gap: 12px;
    }

    .action-button {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .confirm-modal-content {
        max-width: 500px;
        padding: 28px;
    }

    .modal-title {
        font-size: 1.8rem;
    }

    .success-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 14px;
    }

    .feature-item {
        padding: 18px;
    }

    .detail-item {
        padding: 14px 18px;
    }
}

@media (min-width: 769px) {
    .confirm-modal-content {
        max-width: 550px;
    }

    .success-features {
        grid-template-columns: repeat(2, 1fr);
    }
}

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import SubscriptionModal from '../SubscriptionModal/SubscriptionModal';
import './SubscriptionTrigger.css';

const SubscriptionTrigger = () => {
  const [showModal, setShowModal] = useState(false);
  const [hasShownModal, setHasShownModal] = useState(false);

  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const location = useLocation();

  // Check if user needs subscription
  const needsSubscription = () => {
    if (!user || user.isAdmin) return false;
    
    // Check if user has active subscription
    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
      return false;
    }
    
    // Check if subscription is not expired
    if (subscriptionData && subscriptionData.endDate) {
      const endDate = new Date(subscriptionData.endDate);
      const now = new Date();
      if (endDate > now) return false;
    }
    
    return true;
  };

  useEffect(() => {
    // Show subscription modal after login if user needs subscription
    if (needsSubscription() && !hasShownModal) {
      const timer = setTimeout(() => {
        setShowModal(true);
        setHasShownModal(true);
      }, 2000); // Show after 2 seconds

      return () => clearTimeout(timer);
    }
  }, [user, subscriptionData, hasShownModal]);

  const handleSuccess = () => {
    setShowModal(false);
    setHasShownModal(true);
  };

  const handleClose = () => {
    setShowModal(false);
    // Don't set hasShownModal to true on close, so it can show again later
  };

  // Don't render anything if user doesn't need subscription or is on profile/plans page
  if (!needsSubscription()) {
    return null;
  }

  // Don't show overlay on profile or plans pages - let users access these
  const isOnAllowedPage = location.pathname.includes('/profile') || location.pathname.includes('/plans');
  if (isOnAllowedPage) {
    return null;
  }

  return (
    <>
      {/* Subscription Required Overlay */}
      {needsSubscription() && !showModal && (
        <div className="subscription-required-overlay">
          <div className="subscription-prompt">
            <div className="prompt-icon">🔒</div>
            <h3>Premium Access Required</h3>
            <p>Choose a subscription plan to unlock all features</p>
            <div className="subscription-actions">
              <button
                className="choose-plan-btn"
                onClick={() => setShowModal(true)}
              >
                Choose Plan
              </button>
              <button
                className="profile-btn"
                onClick={() => window.location.href = '/user/profile'}
              >
                Update Phone Number
              </button>
              <button
                className="logout-btn"
                onClick={() => {
                  localStorage.removeItem('token');
                  localStorage.removeItem('user');
                  window.location.href = '/login';
                }}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      <SubscriptionModal 
        isOpen={showModal}
        onClose={handleClose}
        onSuccess={handleSuccess}
      />
    </>
  );
};

export default SubscriptionTrigger;

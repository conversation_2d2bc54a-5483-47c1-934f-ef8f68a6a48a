import React, { useEffect, useState } from "react";
import Mo<PERSON> from "react-modal";
import "./WaitingModal.css";

Modal.setAppElement("#root"); // Ensure accessibility for screen readers

const WaitingModal = ({ isOpen, onClose }) => {
    const [dots, setDots] = useState('');
    const [currentStep, setCurrentStep] = useState(0);

    // Animated dots for loading effect
    useEffect(() => {
        const interval = setInterval(() => {
            setDots(prev => prev.length >= 3 ? '' : prev + '.');
        }, 500);
        return () => clearInterval(interval);
    }, []);

    // Progress steps animation
    useEffect(() => {
        if (isOpen) {
            const stepInterval = setInterval(() => {
                setCurrentStep(prev => (prev + 1) % 6);
            }, 3000); // Slower progression for more steps
            return () => clearInterval(stepInterval);
        }
    }, [isOpen]);

    const progressSteps = [
        "Initializing secure connection...",
        "Connecting to payment gateway...",
        "SMS sent to your phone...",
        "Waiting for payment confirmation...",
        "Checking payment status...",
        "Almost done..."
    ];

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            className="waiting-modal-content"
            overlayClassName="waiting-modal-overlay"
        >
            {/* Header Section */}
            <div className="waiting-modal-header">
                <div className="payment-icon-container">
                    <div className="payment-processing-icon">
                        <svg
                            className="payment-icon"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <circle cx="12" cy="12" r="10" stroke="#007BFF" strokeWidth="2" fill="none"/>
                            <path d="M8 12l2 2 4-4" stroke="#007BFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <div className="loading-spinner"></div>
                    </div>
                </div>
                <h2 className="payment-title">Processing Payment{dots}</h2>
                <p className="payment-subtitle">Please wait while we process your payment securely</p>
            </div>

            {/* Progress Section */}
            <div className="waiting-modal-progress">
                <div className="progress-container">
                    <div className="progress-bar">
                        <div className="progress-fill"></div>
                    </div>
                    <p className="progress-text">{progressSteps[currentStep]}</p>
                </div>
            </div>

            {/* Instructions Section */}
            <div className="payment-instructions">
                <div className="instructions-header">
                    <h3>📱 Next Steps</h3>
                </div>
                <div className="instruction-list">
                    <div className="instruction-item">
                        <div className="step-number">1</div>
                        <div className="step-content">
                            <span className="step-title">Check Your Phone</span>
                            <span className="step-text">Look for SMS confirmation message</span>
                        </div>
                    </div>
                    <div className="instruction-item">
                        <div className="step-number">2</div>
                        <div className="step-content">
                            <span className="step-title">Follow Instructions</span>
                            <span className="step-text">Complete the payment as directed</span>
                        </div>
                    </div>
                    <div className="instruction-item">
                        <div className="step-number">3</div>
                        <div className="step-content">
                            <span className="step-title">Wait for Confirmation</span>
                            <span className="step-text">We'll automatically detect when payment is complete</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Security Badge */}
            <div className="security-badge">
                <div className="security-icon">🔒</div>
                <span className="security-text">Secured by ZenoPay</span>
            </div>

            <div className="security-notice">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="#10B981" strokeWidth="2" fill="none"/>
                    <path d="M9 12L11 14L15 10" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span>Your payment is secured with bank-level encryption</span>
            </div>
        </Modal>
    );
};

export default WaitingModal;

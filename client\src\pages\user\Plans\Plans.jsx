import React from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import './Plans.css';

const Plans = () => {
  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const navigate = useNavigate();

  // Check if user has active subscription
  const hasActiveSubscription = () => {
    if (!subscriptionData) return false;
    
    if (subscriptionData.paymentStatus === "paid") {
      if (subscriptionData.status === "active") return true;
      
      // Check if subscription is not expired
      if (subscriptionData.endDate) {
        const endDate = new Date(subscriptionData.endDate);
        const now = new Date();
        return endDate > now;
      }
    }
    return false;
  };

  const isActive = hasActiveSubscription();

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "Not available";
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return "Invalid date";
    }
  };

  // Calculate days remaining
  const getDaysRemaining = (endDate) => {
    if (!endDate) return 0;
    try {
      const end = new Date(endDate);
      const now = new Date();
      const diffTime = end - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return Math.max(0, diffDays);
    } catch {
      return 0;
    }
  };

  return (
    <div className="plans-page">
      <div className="page-header">
        <h1 className="page-title">Subscription Details</h1>
        <p className="page-subtitle">Manage your learning subscription</p>
      </div>

      {isActive ? (
        <div className="active-subscription">
          <div className="subscription-badge">
            <span className="badge-dot"></span>
            Active Subscription
          </div>
          
          <div className="subscription-content">
            <h2 className="subscription-title">
              {subscriptionData?.plan?.title || "Premium Plan"}
            </h2>
            
            <div className="subscription-grid">
              <div className="info-card">
                <div className="info-icon">📅</div>
                <div className="info-text">
                  <span className="info-label">Started</span>
                  <span className="info-value">{formatDate(subscriptionData?.startDate)}</span>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-icon">⏰</div>
                <div className="info-text">
                  <span className="info-label">Expires</span>
                  <span className="info-value">{formatDate(subscriptionData?.endDate)}</span>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-icon">🎯</div>
                <div className="info-text">
                  <span className="info-label">Days Left</span>
                  <span className="info-value highlight">
                    {getDaysRemaining(subscriptionData?.endDate)} days
                  </span>
                </div>
              </div>
              
              <div className="info-card">
                <div className="info-icon">💎</div>
                <div className="info-text">
                  <span className="info-label">Plan Type</span>
                  <span className="info-value">{subscriptionData?.plan?.title || "Premium"}</span>
                </div>
              </div>
            </div>

            <div className="benefits-section">
              <h3>Your Premium Benefits</h3>
              <div className="benefits-list">
                <div className="benefit">📚 Unlimited Quiz Access</div>
                <div className="benefit">🎯 Progress Tracking</div>
                <div className="benefit">🏆 Achievement System</div>
                <div className="benefit">🚀 AI Study Assistant</div>
                <div className="benefit">📖 Study Materials</div>
                <div className="benefit">🎥 Learning Videos</div>
                <div className="benefit">💬 Forum Access</div>
                <div className="benefit">📊 Detailed Analytics</div>
              </div>
            </div>

            <div className="action-buttons">
              <button 
                className="primary-button"
                onClick={() => navigate('/user/hub')}
              >
                Continue Learning 🎓
              </button>
              <button 
                className="secondary-button"
                onClick={() => navigate('/user/profile')}
              >
                Manage Account
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="no-subscription">
          <div className="no-subscription-icon">🔒</div>
          <h3>No Active Subscription</h3>
          <p>You don't have an active subscription. Please choose a plan to unlock premium features.</p>
          <button 
            className="upgrade-button"
            onClick={() => navigate('/user/profile')}
          >
            View Subscription Options
          </button>
        </div>
      )}
    </div>
  );
};

export default Plans;

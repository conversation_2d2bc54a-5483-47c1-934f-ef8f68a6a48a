import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import { getPlans } from "../../../apicalls/plans";
import { addPayment } from "../../../apicalls/payment";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import "./Plans.css";

const Plans = () => {
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paymentLoading, setPaymentLoading] = useState(false);
    const [selectedPlanId, setSelectedPlanId] = useState(null);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [paymentSuccess, setPaymentSuccess] = useState(false);
    
    const { user } = useSelector((state) => state.user);
    const { subscriptionData } = useSelector((state) => state.subscription);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    // Fetch plans on component mount
    useEffect(() => {
        fetchPlans();
    }, []);

    const fetchPlans = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await getPlans();
            setPlans(Array.isArray(response) ? response : []);
        } catch (error) {
            console.error("Error fetching plans:", error);
            setError("Failed to load plans. Please try again.");
            setPlans([]);
        } finally {
            setLoading(false);
        }
    };

    const handlePlanSelect = async (plan) => {
        if (!plan || paymentLoading) return;
        
        if (!user?.phoneNumber) {
            message.error("Please update your phone number in profile to proceed with payment.");
            return;
        }

        try {
            setPaymentLoading(true);
            setSelectedPlanId(plan._id);
            setShowPaymentModal(true);
            
            dispatch(ShowLoading());
            
            const paymentData = {
                planId: plan._id,
                amount: plan.discountedPrice,
                planTitle: plan.title,
                userId: user._id,
                userPhone: user.phoneNumber,
                userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
            };

            const response = await addPayment(paymentData);
            
            if (response.success) {
                message.success("Payment initiated successfully! Please check your phone for SMS confirmation.");
                // In real implementation, you would handle the payment flow here
                setTimeout(() => {
                    setPaymentSuccess(true);
                    setShowPaymentModal(false);
                }, 3000);
            } else {
                throw new Error(response.message || "Payment failed");
            }
            
        } catch (error) {
            console.error("Payment error:", error);
            message.error(error.message || "Payment failed. Please try again.");
            setShowPaymentModal(false);
        } finally {
            setPaymentLoading(false);
            setSelectedPlanId(null);
            dispatch(HideLoading());
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return "Not available";
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return "Invalid date";
        }
    };

    const getDaysRemaining = (endDate) => {
        if (!endDate) return 0;
        try {
            const end = new Date(endDate);
            const now = new Date();
            const diffTime = end - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays);
        } catch {
            return 0;
        }
    };

    // Check if subscription is active - handle missing status field and validate dates
    const isSubscriptionActive = () => {
        if (!subscriptionData || subscriptionData.paymentStatus !== "paid") {
            return false;
        }

        // Check if status is explicitly active
        if (subscriptionData.status === "active") {
            return true;
        }

        // If status is missing but we have payment and dates, check if not expired
        if (!subscriptionData.status && subscriptionData.startDate && subscriptionData.endDate) {
            const endDate = new Date(subscriptionData.endDate);
            const now = new Date();
            return endDate > now; // Not expired
        }

        return false;
    };

    const subscriptionActive = isSubscriptionActive();

    // Debug: Log subscription data
    console.log("🔍 Plans Page Debug:");
    console.log("User:", user);
    console.log("Subscription Data:", subscriptionData);
    console.log("Is Subscription Active:", subscriptionActive);

    if (subscriptionData) {
        console.log("🔍 Detailed Analysis:");
        console.log("- Payment Status:", subscriptionData.paymentStatus);
        console.log("- Status Field:", subscriptionData.status || "MISSING");
        console.log("- Start Date:", subscriptionData.startDate);
        console.log("- End Date:", subscriptionData.endDate);
        console.log("- Plan Title:", subscriptionData.plan?.title);

        if (subscriptionData.endDate) {
            const endDate = new Date(subscriptionData.endDate);
            const now = new Date();
            const isExpired = endDate <= now;
            console.log("- Is Expired:", isExpired);
            console.log("- Days Remaining:", Math.ceil((endDate - now) / (1000 * 60 * 60 * 24)));
        }
    }

    // Loading state
    if (loading) {
        return (
            <div className="plans-container">
                <div className="loading-section">
                    <div className="loading-spinner"></div>
                    <h3>Loading your plans...</h3>
                    <p>Please wait while we fetch the latest subscription options</p>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="plans-container">
                <div className="error-section">
                    <div className="error-icon">⚠️</div>
                    <h3>Oops! Something went wrong</h3>
                    <p>{error}</p>
                    <button className="retry-btn" onClick={fetchPlans}>
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="plans-container">
            {/* Header Section */}
            <div className="plans-header">
                <h1 className="plans-title">Choose Your Learning Plan</h1>
                <p className="plans-subtitle">
                    Unlock your potential with our premium educational content
                </p>
            </div>

            {/* Debug Section - Remove in production */}
            {process.env.NODE_ENV === 'development' && (
                <div style={{
                    background: '#f0f0f0',
                    padding: '1rem',
                    margin: '1rem 0',
                    borderRadius: '8px',
                    fontSize: '0.9rem'
                }}>
                    <h3>🔍 Debug Info:</h3>
                    <p><strong>User:</strong> {user?.name || 'Not logged in'}</p>
                    <p><strong>Subscription Data:</strong> {subscriptionData ? 'Available' : 'Not available'}</p>
                    {subscriptionData && (
                        <>
                            <p><strong>Payment Status:</strong> {subscriptionData.paymentStatus}</p>
                            <p><strong>Status:</strong> {subscriptionData.status}</p>
                            <p><strong>Start Date:</strong> {subscriptionData.startDate}</p>
                            <p><strong>End Date:</strong> {subscriptionData.endDate}</p>
                            <p><strong>Plan:</strong> {subscriptionData.plan?.title || 'No plan'}</p>
                        </>
                    )}
                    <p><strong>Is Active:</strong> {subscriptionActive ? 'YES' : 'NO'}</p>
                </div>
            )}

            {/* Current Subscription Section */}
            {subscriptionActive && (
                <div className="current-subscription">
                    <div className="subscription-header">
                        <div className="status-badge active">
                            <span className="status-dot"></span>
                            Active Subscription
                        </div>
                        <h2 className="subscription-title">
                            {subscriptionData?.plan?.title || "Premium Plan"}
                        </h2>
                    </div>

                    <div className="subscription-details">
                        <div className="detail-card">
                            <div className="detail-icon">📅</div>
                            <div className="detail-content">
                                <span className="detail-label">Started</span>
                                <span className="detail-value">
                                    {formatDate(subscriptionData?.startDate)}
                                </span>
                            </div>
                        </div>

                        <div className="detail-card">
                            <div className="detail-icon">⏰</div>
                            <div className="detail-content">
                                <span className="detail-label">Expires</span>
                                <span className="detail-value">
                                    {formatDate(subscriptionData?.endDate)}
                                </span>
                            </div>
                        </div>

                        <div className="detail-card">
                            <div className="detail-icon">🎯</div>
                            <div className="detail-content">
                                <span className="detail-label">Days Remaining</span>
                                <span className="detail-value highlight">
                                    {getDaysRemaining(subscriptionData?.endDate)} days
                                </span>
                            </div>
                        </div>

                        <div className="detail-card">
                            <div className="detail-icon">💎</div>
                            <div className="detail-content">
                                <span className="detail-label">Plan Type</span>
                                <span className="detail-value">
                                    {subscriptionData?.plan?.title || "Premium"}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="subscription-benefits">
                        <h3>Your Premium Benefits</h3>
                        <div className="benefits-grid">
                            <div className="benefit-item">
                                <span className="benefit-icon">📚</span>
                                <span className="benefit-text">Unlimited Quiz Access</span>
                            </div>
                            <div className="benefit-item">
                                <span className="benefit-icon">🎯</span>
                                <span className="benefit-text">Progress Tracking</span>
                            </div>
                            <div className="benefit-item">
                                <span className="benefit-icon">🏆</span>
                                <span className="benefit-text">Achievement Badges</span>
                            </div>
                            <div className="benefit-item">
                                <span className="benefit-icon">🚀</span>
                                <span className="benefit-text">AI Study Assistant</span>
                            </div>
                        </div>
                    </div>

                    <div className="subscription-actions">
                        <button 
                            className="action-btn primary"
                            onClick={() => navigate('/user/hub')}
                        >
                            Continue Learning 🎓
                        </button>
                        <button 
                            className="action-btn secondary"
                            onClick={() => navigate('/user/profile')}
                        >
                            Manage Account
                        </button>
                    </div>
                </div>
            )}

            {/* Available Plans Section */}
            {!subscriptionActive && (
                <div className="available-plans">
                    <h2 className="section-title">Available Plans</h2>
                    
                    {plans.length === 0 ? (
                        <div className="no-plans">
                            <div className="no-plans-icon">📋</div>
                            <h3>No Plans Available</h3>
                            <p>Please check back later for subscription options.</p>
                        </div>
                    ) : (
                        <div className="plans-grid">
                            {plans.map((plan) => (
                                <div key={plan._id} className="plan-card">
                                    <div className="plan-header">
                                        <h3 className="plan-name">{plan.title}</h3>
                                        {plan.title?.toLowerCase().includes('glimp') && (
                                            <div className="plan-badge">🚀 Quick Start</div>
                                        )}
                                    </div>

                                    <div className="plan-pricing">
                                        <div className="price-main">
                                            {plan.discountedPrice?.toLocaleString() || '0'} TZS
                                        </div>
                                        {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (
                                            <div className="price-original">
                                                {plan.actualPrice.toLocaleString()} TZS
                                            </div>
                                        )}
                                        <div className="price-period">
                                            {plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'}
                                        </div>
                                    </div>

                                    <div className="plan-features">
                                        {plan.features?.map((feature, index) => (
                                            <div key={index} className="feature-item">
                                                <span className="feature-check">✓</span>
                                                <span className="feature-text">{feature}</span>
                                            </div>
                                        )) || (
                                            <div className="feature-item">
                                                <span className="feature-check">✓</span>
                                                <span className="feature-text">Premium access included</span>
                                            </div>
                                        )}
                                    </div>

                                    <button
                                        className={`plan-btn ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`}
                                        onClick={() => handlePlanSelect(plan)}
                                        disabled={paymentLoading}
                                    >
                                        {paymentLoading && selectedPlanId === plan._id ? (
                                            <>
                                                <span className="btn-spinner"></span>
                                                Processing...
                                            </>
                                        ) : (
                                            plan.title?.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'
                                        )}
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {/* Payment Modal */}
            {showPaymentModal && (
                <div className="payment-modal-overlay">
                    <div className="payment-modal">
                        <div className="payment-content">
                            <div className="payment-icon">
                                <div className="payment-spinner"></div>
                            </div>
                            <h3>Processing Payment</h3>
                            <p>Please check your phone for SMS confirmation</p>
                            <div className="payment-steps">
                                <div className="step">1. Check your SMS</div>
                                <div className="step">2. Follow the instructions</div>
                                <div className="step">3. Complete payment</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Success Modal */}
            {paymentSuccess && (
                <div className="success-modal-overlay">
                    <div className="success-modal">
                        <div className="success-content">
                            <div className="success-icon">🎉</div>
                            <h3>Payment Successful!</h3>
                            <p>Your subscription has been activated</p>
                            <button 
                                className="success-btn"
                                onClick={() => {
                                    setPaymentSuccess(false);
                                    window.location.reload();
                                }}
                            >
                                Continue Learning
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Plans;

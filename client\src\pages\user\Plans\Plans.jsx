import React, { useEffect, useState } from "react";
import { getPlans } from "../../../apicalls/plans";
import "./Plans.css";
import ConfirmModal from "./components/ConfirmModal";
import WaitingModal from "./components/WaitingModal";
import { addPayment } from "../../../apicalls/payment";
import { useDispatch, useSelector } from "react-redux";
import { setPaymentVerificationNeeded } from "../../../redux/paymentSlice";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { message } from "antd";
import { useNavigate } from "react-router-dom";

const Plans = () => {
    const [plans, setPlans] = useState([]);
    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);
    const [paymentInProgress, setPaymentInProgress] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const { user } = useSelector((state) => state.user);
    const { subscriptionData } = useSelector((state) => state.subscription);
    const dispatch = useDispatch();
    const navigate = useNavigate();



    useEffect(() => {
        const fetchPlans = async () => {
            try {
                const response = await getPlans();
                setPlans(response);
            } catch (error) {
                console.error("Error fetching plans:", error);
            }
        };

        fetchPlans();
    }, []);

    const transactionDetails = {
        amount: selectedPlan?.discountedPrice || 'N/A',
        currency: "TZS",
        destination: "brainwave.zone",
    };


    const handlePaymentStart = async (plan) => {
        setSelectedPlan(plan);
        try {
            dispatch(ShowLoading());
            console.log('💳 Initiating payment for plan:', plan.title);

            const response = await addPayment({ plan });
            console.log('📥 Payment response:', response);

            if (response.success) {
                localStorage.setItem("order_id", response.order_id);
                setWaitingModalOpen(true);
                setPaymentInProgress(true);
                dispatch(setPaymentVerificationNeeded(true));

                // Show success message - confidential payment processing
                message.success({
                    content: `🎉 Payment request initiated successfully! Please check your phone for SMS confirmation to complete the payment.`,
                    duration: 6,
                    style: {
                        marginTop: '20px',
                        fontSize: '16px'
                    }
                });
            } else {
                message.error(response.message || "Payment initiation failed. Please try again.");
            }
        } catch (error) {
            console.error("❌ Error processing payment:", error);
            message.error("Unable to process payment. Please try again.");
        } finally {
            dispatch(HideLoading());
        }
    };


    useEffect(() => {
        console.log("subscription Data in Plans", subscriptionData)
        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === "paid" && paymentInProgress) {
            setWaitingModalOpen(false);
            setConfirmModalOpen(true);
            setPaymentInProgress(false);
        }
    }, [user, subscriptionData]);

    return (
        <div>
            {!user ?
                <>
                </>
                :
                !user.paymentRequired ?
                    <div className="no-plan-required">
                        <div className="no-plan-content">
                            <h2>No Plan Required</h2>
                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>
                        </div>
                    </div>
                    :
                    subscriptionData?.paymentStatus !== "paid" ?
                        <div className="plans-container">
                            {plans
                                .sort((a, b) => {
                                    // Sort order: Glimp Plan first, then Basic Membership, then others
                                    if (a.title === "Glimp Plan") return -1;
                                    if (b.title === "Glimp Plan") return 1;
                                    if (a.title === "Basic Membership") return -1;
                                    if (b.title === "Basic Membership") return 1;
                                    return 0;
                                })
                                .map((plan) => (
                                <div
                                    key={plan._id}
                                    className={`plan-card ${
                                        plan.title === "Basic Membership" ? "basic" :
                                        plan.title === "Glimp Plan" ? "glimp" : ""
                                    }`}
                                >
                                    {plan.title === "Basic Membership" && (
                                        <div className="most-popular-label">MOST POPULAR</div>
                                    )}
                                    {plan.title === "Glimp Plan" && (
                                        <div className="glimp-label">QUICK START</div>
                                    )}

                                    <div className="plan-header">
                                        <h2 className="plan-title">{plan.title}</h2>
                                        <div className="plan-duration-highlight">
                                            <span className="duration-number">{plan.duration}</span>
                                            <span className="duration-text">Month{plan.duration > 1 ? 's' : ''}</span>
                                        </div>
                                    </div>

                                    <div className="plan-pricing">
                                        <p className="plan-actual-price">
                                            {plan.actualPrice.toLocaleString()} TZS
                                        </p>
                                        <p className="plan-discounted-price">
                                            {plan.discountedPrice.toLocaleString()} TZS
                                        </p>
                                        <span className="plan-discount-tag">
                                            {plan.discountPercentage}% OFF
                                        </span>
                                    </div>

                                    <div className="plan-value">
                                        <span className="value-text">
                                            {Math.round(plan.discountedPrice / plan.duration).toLocaleString()} TZS/month
                                        </span>
                                    </div>

                                    <button className="plan-button"
                                        onClick={() => handlePaymentStart(plan)}
                                    >
                                        {plan.title === "Glimp Plan" ? "🚀 Start Quick" : "Choose Plan"}
                                    </button>

                                    <ul className="plan-features">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="plan-feature">
                                                <span className="plan-feature-icon">✔</span>
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            ))}
                        </div>
                        :
                        <div className="current-subscription-container">
                            {/* Header Section */}
                            <div className="current-plan-header">
                                <div className="plan-status-badge">
                                    <div className="status-icon">
                                        <svg
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <circle cx="12" cy="12" r="10" fill="#10B981"/>
                                            <path d="m9 12 2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <span className="status-text">Active Subscription</span>
                                </div>
                                <h2 className="current-plan-title">{subscriptionData.plan.title}</h2>
                                <p className="current-plan-subtitle">You're currently enjoying premium access</p>
                            </div>

                            {/* Plan Details Card */}
                            <div className="current-plan-details">
                                <div className="plan-info-grid">
                                    <div className="plan-info-item">
                                        <div className="info-icon">📅</div>
                                        <div className="info-content">
                                            <span className="info-label">Start Date</span>
                                            <span className="info-value">{new Date(subscriptionData.startDate).toLocaleDateString()}</span>
                                        </div>
                                    </div>

                                    <div className="plan-info-item">
                                        <div className="info-icon">⏰</div>
                                        <div className="info-content">
                                            <span className="info-label">End Date</span>
                                            <span className="info-value">{new Date(subscriptionData.endDate).toLocaleDateString()}</span>
                                        </div>
                                    </div>

                                    <div className="plan-info-item">
                                        <div className="info-icon">💎</div>
                                        <div className="info-content">
                                            <span className="info-label">Plan Type</span>
                                            <span className="info-value">{subscriptionData.plan.title}</span>
                                        </div>
                                    </div>

                                    <div className="plan-info-item">
                                        <div className="info-icon">🎯</div>
                                        <div className="info-content">
                                            <span className="info-label">Status</span>
                                            <span className="info-value status-active">Active</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Plan Features */}
                                <div className="current-plan-features">
                                    <h3 className="features-title">✨ Your Premium Benefits</h3>
                                    <div className="features-grid">
                                        <div className="feature-benefit">
                                            <span className="benefit-icon">📚</span>
                                            <span className="benefit-text">Unlimited Quiz Access</span>
                                        </div>
                                        <div className="feature-benefit">
                                            <span className="benefit-icon">🎯</span>
                                            <span className="benefit-text">Progress Tracking</span>
                                        </div>
                                        <div className="feature-benefit">
                                            <span className="benefit-icon">🏆</span>
                                            <span className="benefit-text">Achievement Badges</span>
                                        </div>
                                        <div className="feature-benefit">
                                            <span className="benefit-icon">🚀</span>
                                            <span className="benefit-text">AI Study Assistant</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="current-plan-actions">
                                    <button
                                        className="action-btn primary"
                                        onClick={() => window.location.href = '/user/hub'}
                                    >
                                        Continue Learning 🎓
                                    </button>
                                    <button
                                        className="action-btn secondary"
                                        onClick={() => window.location.href = '/user/profile'}
                                    >
                                        Manage Subscription
                                    </button>
                                </div>
                            </div>
                        </div>
            }

            <WaitingModal
                isOpen={isWaitingModalOpen}
                onClose={() => setWaitingModalOpen(false)}
            />

            <ConfirmModal
                isOpen={isConfirmModalOpen}
                onClose={() => setConfirmModalOpen(false)}
                transaction={transactionDetails}
            />
        </div>
    );
};

export default Plans;

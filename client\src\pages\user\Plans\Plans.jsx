import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { message } from "antd";
import { getPlans } from "../../../apicalls/plans";
import { addPayment } from "../../../apicalls/payment";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import WaitingModal from "./components/WaitingModal";
import ConfirmModal from "./components/ConfirmModal";
import "./Plans.css";

const Plans = () => {
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paymentLoading, setPaymentLoading] = useState(false);
    const [selectedPlanId, setSelectedPlanId] = useState(null);
    const [showWaitingModal, setShowWaitingModal] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [transactionData, setTransactionData] = useState(null);
    
    const { user } = useSelector((state) => state.user);
    const { subscriptionData } = useSelector((state) => state.subscription);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    useEffect(() => {
        fetchPlans();
    }, []);

    const fetchPlans = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await getPlans();
            setPlans(Array.isArray(response) ? response : []);
        } catch (error) {
            console.error("Error fetching plans:", error);
            setError("Failed to load plans. Please try again.");
            setPlans([]);
        } finally {
            setLoading(false);
        }
    };

    const hasActiveSubscription = () => {
        if (!subscriptionData) return false;
        
        if (subscriptionData.paymentStatus === "paid") {
            if (subscriptionData.status === "active") return true;
            
            if (subscriptionData.endDate) {
                const endDate = new Date(subscriptionData.endDate);
                const now = new Date();
                return endDate > now;
            }
        }
        return false;
    };

    const isActive = hasActiveSubscription();

    const handlePlanSelect = async (plan) => {
        if (!plan || paymentLoading) return;
        
        if (!user?.phoneNumber) {
            message.error("Please update your phone number in profile to proceed with payment.");
            return;
        }

        try {
            setPaymentLoading(true);
            setSelectedPlanId(plan._id);
            setShowWaitingModal(true); // Show beautiful waiting modal

            const paymentData = {
                plan: plan,  // Send the complete plan object
                userId: user._id,
                userPhone: user.phoneNumber,
                userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
            };

            const response = await addPayment(paymentData);

            if (response.success) {
                // Store transaction data for success modal
                setTransactionData({
                    amount: plan.discountedPrice,
                    planTitle: plan.title,
                    orderId: response.order_id,
                    status: 'success'
                });

                // Hide waiting modal and show success modal
                setShowWaitingModal(false);
                setShowSuccessModal(true);

                message.success("Payment initiated successfully! Please check your phone for SMS confirmation.");
            } else {
                throw new Error(response.message || "Payment failed");
            }

        } catch (error) {
            console.error("Payment error:", error);
            setShowWaitingModal(false); // Hide waiting modal on error
            message.error(error.message || "Payment failed. Please try again.");
        } finally {
            setPaymentLoading(false);
            setSelectedPlanId(null);
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return "Not available";
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return "Invalid date";
        }
    };

    const getDaysRemaining = (endDate) => {
        if (!endDate) return 0;
        try {
            const end = new Date(endDate);
            const now = new Date();
            const diffTime = end - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays);
        } catch {
            return 0;
        }
    };

    if (loading) {
        return (
            <div className="plans-page">
                <div className="loading-container">
                    <div className="spinner"></div>
                    <h3>Loading Plans...</h3>
                    <p>Please wait while we fetch your subscription options</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="plans-page">
                <div className="error-container">
                    <div className="error-icon">⚠️</div>
                    <h3>Something went wrong</h3>
                    <p>{error}</p>
                    <button className="retry-button" onClick={fetchPlans}>
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="plans-page">
            <div className="page-header">
                <h1 className="page-title">Your Learning Journey</h1>
                <p className="page-subtitle">Choose the perfect plan to unlock your potential</p>
            </div>

            {isActive && (
                <div className="active-subscription">
                    <div className="subscription-badge">
                        <span className="badge-dot"></span>
                        Active Subscription
                    </div>
                    
                    <div className="subscription-content">
                        <h2 className="subscription-title">
                            {subscriptionData?.plan?.title || "Premium Plan"}
                        </h2>
                        
                        <div className="subscription-grid">
                            <div className="info-card">
                                <div className="info-icon">📅</div>
                                <div className="info-text">
                                    <span className="info-label">Started</span>
                                    <span className="info-value">{formatDate(subscriptionData?.startDate)}</span>
                                </div>
                            </div>
                            
                            <div className="info-card">
                                <div className="info-icon">⏰</div>
                                <div className="info-text">
                                    <span className="info-label">Expires</span>
                                    <span className="info-value">{formatDate(subscriptionData?.endDate)}</span>
                                </div>
                            </div>
                            
                            <div className="info-card">
                                <div className="info-icon">🎯</div>
                                <div className="info-text">
                                    <span className="info-label">Days Left</span>
                                    <span className="info-value highlight">
                                        {getDaysRemaining(subscriptionData?.endDate)} days
                                    </span>
                                </div>
                            </div>
                            
                            <div className="info-card">
                                <div className="info-icon">💎</div>
                                <div className="info-text">
                                    <span className="info-label">Plan Type</span>
                                    <span className="info-value">{subscriptionData?.plan?.title || "Premium"}</span>
                                </div>
                            </div>
                        </div>

                        <div className="benefits-section">
                            <h3>Your Benefits</h3>
                            <div className="benefits-list">
                                <div className="benefit">📚 Unlimited Quiz Access</div>
                                <div className="benefit">🎯 Progress Tracking</div>
                                <div className="benefit">🏆 Achievement System</div>
                                <div className="benefit">🚀 AI Study Assistant</div>
                            </div>
                        </div>

                        <div className="action-buttons">
                            <button 
                                className="primary-button"
                                onClick={() => navigate('/user/hub')}
                            >
                                Continue Learning 🎓
                            </button>
                            <button 
                                className="secondary-button"
                                onClick={() => navigate('/user/profile')}
                            >
                                Manage Account
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {!isActive && (
                <div className="plans-section">
                    <h2 className="section-title">Choose Your Plan</h2>
                    
                    {plans.length === 0 ? (
                        <div className="no-plans">
                            <div className="no-plans-icon">📋</div>
                            <h3>No Plans Available</h3>
                            <p>Please check back later for subscription options.</p>
                        </div>
                    ) : (
                        <div className="plans-grid">
                            {plans.map((plan) => (
                                <div key={plan._id} className="plan-card">
                                    <div className="plan-header">
                                        <h3 className="plan-title">{plan.title}</h3>
                                        {plan.title?.toLowerCase().includes('glimp') && (
                                            <div className="plan-badge">🚀 Quick Start</div>
                                        )}
                                    </div>

                                    <div className="plan-price">
                                        <div className="price-main">
                                            {plan.discountedPrice?.toLocaleString() || '0'} TZS
                                        </div>
                                        {plan.actualPrice && plan.actualPrice !== plan.discountedPrice && (
                                            <div className="price-old">
                                                {plan.actualPrice.toLocaleString()} TZS
                                            </div>
                                        )}
                                        <div className="price-period">
                                            {plan.duration ? `${plan.duration} month${plan.duration > 1 ? 's' : ''}` : 'One-time'}
                                        </div>
                                    </div>

                                    <div className="plan-features">
                                        {plan.features?.map((feature, index) => (
                                            <div key={index} className="feature">
                                                <span className="feature-check">✓</span>
                                                <span className="feature-text">{feature}</span>
                                            </div>
                                        )) || (
                                            <div className="feature">
                                                <span className="feature-check">✓</span>
                                                <span className="feature-text">Premium access included</span>
                                            </div>
                                        )}
                                    </div>

                                    <button
                                        className={`plan-button ${paymentLoading && selectedPlanId === plan._id ? 'loading' : ''}`}
                                        onClick={() => handlePlanSelect(plan)}
                                        disabled={paymentLoading}
                                    >
                                        {paymentLoading && selectedPlanId === plan._id ? (
                                            <>
                                                <span className="button-spinner"></span>
                                                Processing...
                                            </>
                                        ) : (
                                            plan.title?.toLowerCase().includes('glimp') ? '🚀 Start Quick' : 'Choose Plan'
                                        )}
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {/* Beautiful Payment Modals */}
            <WaitingModal
                isOpen={showWaitingModal}
                onClose={() => setShowWaitingModal(false)}
            />

            <ConfirmModal
                isOpen={showSuccessModal}
                onClose={() => {
                    setShowSuccessModal(false);
                    // Optionally redirect to dashboard or refresh subscription data
                    window.location.reload(); // Refresh to show updated subscription
                }}
                transaction={transactionData}
            />
        </div>
    );
};

export default Plans;
